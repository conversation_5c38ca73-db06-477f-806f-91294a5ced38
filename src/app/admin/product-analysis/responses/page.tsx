/**
 * 回應分析頁面
 * 分析 AI 引擎回應中的品牌提及情況
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  Search, 
  Filter, 
  Download, 
  Eye, 
  MessageSquare, 
  TrendingUp,
  TrendingDown,
  MoreHorizontal,
  Calendar,
  RefreshCw
} from 'lucide-react';
import { cn } from '@/lib/utils';

// 模擬回應分析數據
const mockResponseData = {
  summary: {
    totalResponses: 1247,
    brandMentions: 892,
    averagePosition: 2.3,
    sentimentScore: 78.5,
  },
  responses: [
    {
      id: '1',
      query: '最佳項目管理工具推薦',
      aiEngine: 'ChatGPT',
      response: '對於項目管理，我推薦幾個優秀的工具：1. Asana - 適合團隊協作，界面直觀易用...',
      brandMentions: [
        { brand: 'Asana', position: 1, prominence: 'high', sentiment: 'positive' },
        { brand: 'Trello', position: 3, prominence: 'medium', sentiment: 'positive' },
      ],
      timestamp: '2024-06-24T10:30:00Z',
      relevanceScore: 92.5,
    },
    {
      id: '2',
      query: '企業級CRM系統比較',
      aiEngine: 'Gemini',
      response: '在企業級CRM系統中，Salesforce 是市場領導者，提供全面的客戶關係管理功能...',
      brandMentions: [
        { brand: 'Salesforce', position: 1, prominence: 'high', sentiment: 'positive' },
        { brand: 'HubSpot', position: 2, prominence: 'high', sentiment: 'positive' },
        { brand: 'Microsoft Dynamics', position: 4, prominence: 'medium', sentiment: 'neutral' },
      ],
      timestamp: '2024-06-24T09:15:00Z',
      relevanceScore: 88.7,
    },
    {
      id: '3',
      query: '雲端儲存服務推薦',
      aiEngine: 'Perplexity',
      response: 'Google Drive 提供15GB免費空間，與Google生態系統完美整合。Dropbox 在文件同步方面表現優異...',
      brandMentions: [
        { brand: 'Google Drive', position: 1, prominence: 'high', sentiment: 'positive' },
        { brand: 'Dropbox', position: 2, prominence: 'high', sentiment: 'positive' },
        { brand: 'OneDrive', position: 3, prominence: 'medium', sentiment: 'neutral' },
      ],
      timestamp: '2024-06-24T08:45:00Z',
      relevanceScore: 85.2,
    },
    {
      id: '4',
      query: '電子商務平台選擇指南',
      aiEngine: 'Claude',
      response: 'Shopify 是最受歡迎的電商平台之一，提供豐富的模板和插件生態系統...',
      brandMentions: [
        { brand: 'Shopify', position: 1, prominence: 'high', sentiment: 'positive' },
        { brand: 'WooCommerce', position: 2, prominence: 'medium', sentiment: 'positive' },
        { brand: 'BigCommerce', position: 4, prominence: 'low', sentiment: 'neutral' },
      ],
      timestamp: '2024-06-23T16:20:00Z',
      relevanceScore: 91.3,
    },
  ],
};

export default function ResponseAnalysisPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [data, setData] = useState(mockResponseData);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedEngine, setSelectedEngine] = useState('all');
  const [selectedSentiment, setSelectedSentiment] = useState('all');

  // 格式化時間
  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleString('zh-TW');
  };

  // 獲取情感顏色
  const getSentimentColor = (sentiment: string) => {
    switch (sentiment) {
      case 'positive': return 'text-green-600 bg-green-50';
      case 'negative': return 'text-red-600 bg-red-50';
      case 'neutral': return 'text-gray-600 bg-gray-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  // 獲取顯著性顏色
  const getProminenceColor = (prominence: string) => {
    switch (prominence) {
      case 'high': return 'text-blue-600 bg-blue-50';
      case 'medium': return 'text-yellow-600 bg-yellow-50';
      case 'low': return 'text-gray-600 bg-gray-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  // 獲取 AI 引擎顏色
  const getEngineColor = (engine: string) => {
    switch (engine) {
      case 'ChatGPT': return 'bg-green-100 text-green-800';
      case 'Gemini': return 'bg-blue-100 text-blue-800';
      case 'Perplexity': return 'bg-purple-100 text-purple-800';
      case 'Claude': return 'bg-orange-100 text-orange-800';
      case 'Copilot': return 'bg-indigo-100 text-indigo-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // 刷新數據
  const handleRefresh = async () => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  };

  return (
    <div className="space-y-6">
      {/* 頁面標題和操作 */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">回應分析</h2>
          <p className="text-gray-600">分析 AI 引擎回應中的品牌提及情況</p>
        </div>
        <div className="flex items-center gap-2">
          <Button onClick={handleRefresh} disabled={isLoading} variant="outline">
            <RefreshCw className={cn('w-4 h-4 mr-2', isLoading && 'animate-spin')} />
            刷新
          </Button>
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            導出
          </Button>
        </div>
      </div>

      {/* 統計概覽 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">總回應數</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.summary.totalResponses.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              已分析的 AI 回應總數
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">品牌提及</CardTitle>
            <Eye className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.summary.brandMentions.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              包含品牌提及的回應數
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">平均位置</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.summary.averagePosition.toFixed(1)}</div>
            <p className="text-xs text-muted-foreground">
              品牌在回應中的平均位置
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">情感分數</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {data.summary.sentimentScore.toFixed(1)}%
            </div>
            <p className="text-xs text-muted-foreground">
              正面情感提及的百分比
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 篩選和搜索 */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="搜索查詢內容或品牌名稱..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select 
                value={selectedEngine}
                onChange={(e) => setSelectedEngine(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm"
              >
                <option value="all">所有引擎</option>
                <option value="ChatGPT">ChatGPT</option>
                <option value="Gemini">Gemini</option>
                <option value="Perplexity">Perplexity</option>
                <option value="Claude">Claude</option>
                <option value="Copilot">Copilot</option>
              </select>
              <select 
                value={selectedSentiment}
                onChange={(e) => setSelectedSentiment(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm"
              >
                <option value="all">所有情感</option>
                <option value="positive">正面</option>
                <option value="neutral">中性</option>
                <option value="negative">負面</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 回應列表 */}
      <div className="space-y-4">
        {data.responses.map((response) => (
          <Card key={response.id}>
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <Badge className={getEngineColor(response.aiEngine)}>
                      {response.aiEngine}
                    </Badge>
                    <span className="text-sm text-gray-500">
                      相關度: {response.relevanceScore.toFixed(1)}%
                    </span>
                    <span className="text-sm text-gray-500">
                      {formatTime(response.timestamp)}
                    </span>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    {response.query}
                  </h3>
                </div>
                <Button variant="ghost" size="sm">
                  <MoreHorizontal className="w-4 h-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* 回應內容 */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <p className="text-sm text-gray-700 line-clamp-3">
                    {response.response}
                  </p>
                </div>

                {/* 品牌提及 */}
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-2">品牌提及</h4>
                  <div className="flex flex-wrap gap-2">
                    {response.brandMentions.map((mention, index) => (
                      <div key={index} className="flex items-center gap-2 bg-white border rounded-lg px-3 py-2">
                        <span className="font-medium text-sm">{mention.brand}</span>
                        <Badge variant="outline" className="text-xs">
                          位置 {mention.position}
                        </Badge>
                        <Badge className={cn('text-xs', getProminenceColor(mention.prominence))}>
                          {mention.prominence === 'high' ? '高' : 
                           mention.prominence === 'medium' ? '中' : '低'}
                        </Badge>
                        <Badge className={cn('text-xs', getSentimentColor(mention.sentiment))}>
                          {mention.sentiment === 'positive' ? '正面' :
                           mention.sentiment === 'negative' ? '負面' : '中性'}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* 分頁 */}
      <div className="flex justify-center">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" disabled>
            上一頁
          </Button>
          <span className="text-sm text-gray-600">第 1 頁，共 10 頁</span>
          <Button variant="outline" size="sm">
            下一頁
          </Button>
        </div>
      </div>
    </div>
  );
}
