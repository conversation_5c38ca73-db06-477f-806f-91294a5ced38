/**
 * 產品分析模組佈局組件
 * 提供產品分析功能的統一佈局和導航
 */

'use client';

import React from 'react';
import { usePathname } from 'next/navigation';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import { 
  TrendingUp, 
  MessageSquare, 
  Link as LinkIcon, 
  Hash, 
  FileText, 
  BarChart3,
  Settings,
  ChevronRight
} from 'lucide-react';

// 產品分析導航項目
const productAnalysisNavItems = [
  {
    href: '/admin/product-analysis',
    label: '分析儀表板',
    icon: TrendingUp,
    description: 'AI 搜尋引擎品牌可見度總覽',
    exact: true
  },
  {
    href: '/admin/product-analysis/responses',
    label: '回應分析',
    icon: MessageSquare,
    description: '分析 AI 引擎回應中的品牌提及'
  },
  {
    href: '/admin/product-analysis/citations',
    label: '引用分析',
    icon: LinkIcon,
    description: '分析引用來源的權威性和影響力'
  },
  {
    href: '/admin/product-analysis/topics',
    label: '主題分析',
    icon: Hash,
    description: '分析品牌在特定主題的表現'
  },
  {
    href: '/admin/product-analysis/content',
    label: '內容分析',
    icon: FileText,
    description: '優化內容以提升 AI 可見度'
  },
  {
    href: '/admin/product-analysis/reports',
    label: '分析報告',
    icon: BarChart3,
    description: '生成和管理分析報告'
  },
  {
    href: '/admin/product-analysis/settings',
    label: '分析設定',
    icon: Settings,
    description: '配置產品分析參數'
  },
];

interface ProductAnalysisLayoutProps {
  children: React.ReactNode;
}

export default function ProductAnalysisLayout({ children }: ProductAnalysisLayoutProps) {
  const pathname = usePathname();

  // 檢查當前路徑是否匹配導航項目
  const isActiveNavItem = (item: typeof productAnalysisNavItems[0]) => {
    if (item.exact) {
      return pathname === item.href;
    }
    return pathname.startsWith(item.href);
  };

  // 獲取當前頁面標題
  const getCurrentPageTitle = () => {
    const currentItem = productAnalysisNavItems.find(item => isActiveNavItem(item));
    return currentItem?.label || '產品分析';
  };

  // 獲取麵包屑導航
  const getBreadcrumbs = () => {
    const breadcrumbs = [
      { label: '管理後台', href: '/admin' },
      { label: '產品分析', href: '/admin/product-analysis' }
    ];

    const currentItem = productAnalysisNavItems.find(item => isActiveNavItem(item));
    if (currentItem && !currentItem.exact) {
      breadcrumbs.push({ label: currentItem.label, href: currentItem.href });
    }

    return breadcrumbs;
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 頁面標題和麵包屑 */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-6">
            {/* 麵包屑導航 */}
            <nav className="flex items-center space-x-2 text-sm text-gray-500 mb-4">
              {getBreadcrumbs().map((crumb, index) => (
                <React.Fragment key={crumb.href}>
                  {index > 0 && <ChevronRight className="w-4 h-4" />}
                  <Link
                    href={crumb.href}
                    className={cn(
                      'hover:text-gray-700 transition-colors',
                      index === getBreadcrumbs().length - 1 && 'text-gray-900 font-medium'
                    )}
                  >
                    {crumb.label}
                  </Link>
                </React.Fragment>
              ))}
            </nav>

            {/* 頁面標題 */}
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">
                  {getCurrentPageTitle()}
                </h1>
                <p className="mt-2 text-gray-600">
                  {productAnalysisNavItems.find(item => isActiveNavItem(item))?.description || 
                   'AI 搜尋引擎品牌可見度分析平台'}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 主要內容區域 */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* 側邊導航 */}
          <div className="lg:w-64 flex-shrink-0">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
              <h3 className="text-sm font-semibold text-gray-900 mb-4">功能導航</h3>
              <nav className="space-y-2">
                {productAnalysisNavItems.map((item) => {
                  const Icon = item.icon;
                  const isActive = isActiveNavItem(item);
                  
                  return (
                    <Link
                      key={item.href}
                      href={item.href}
                      className={cn(
                        'flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors',
                        isActive
                          ? 'bg-indigo-50 text-indigo-700 border-l-4 border-indigo-500'
                          : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                      )}
                    >
                      <Icon className={cn(
                        'w-5 h-5 mr-3',
                        isActive ? 'text-indigo-500' : 'text-gray-400'
                      )} />
                      <span>{item.label}</span>
                    </Link>
                  );
                })}
              </nav>
            </div>

            {/* 快速統計 */}
            <div className="mt-6 bg-white rounded-lg shadow-sm border border-gray-200 p-4">
              <h3 className="text-sm font-semibold text-gray-900 mb-4">快速統計</h3>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">總品牌數</span>
                  <span className="text-sm font-semibold text-gray-900">12</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">本月分析</span>
                  <span className="text-sm font-semibold text-gray-900">248</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">平均可見度</span>
                  <span className="text-sm font-semibold text-green-600">78.5%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">活躍報告</span>
                  <span className="text-sm font-semibold text-gray-900">6</span>
                </div>
              </div>
            </div>
          </div>

          {/* 主要內容 */}
          <div className="flex-1 min-w-0">
            {children}
          </div>
        </div>
      </div>
    </div>
  );
}
