/**
 * 主題分析頁面
 * 分析品牌在特定主題的表現
 */

'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Progress } from '@/components/ui/progress';
import { 
  Search, 
  Download, 
  Hash, 
  TrendingUp,
  TrendingDown,
  Target,
  Users,
  RefreshCw,
  MoreHorizontal,
  ArrowUp,
  ArrowDown,
  Minus
} from 'lucide-react';
import { cn } from '@/lib/utils';

// 模擬主題分析數據
const mockTopicData = {
  summary: {
    totalTopics: 45,
    averageVisibility: 76.8,
    topPerformingTopics: 12,
    improvementOpportunities: 8,
  },
  topics: [
    {
      id: '1',
      topic: '項目管理工具',
      category: '生產力軟體',
      brandPerformance: {
        visibility: 89.2,
        position: 1.8,
        shareOfVoice: 34.5,
        change: 5.2
      },
      competitors: [
        { brandName: 'Asana', visibility: 92.1, position: 1.2, shareOfVoice: 38.7, change: 2.3 },
        { brandName: 'Trello', visibility: 85.6, position: 2.1, shareOfVoice: 28.9, change: -1.5 },
        { brandName: 'Monday.com', visibility: 82.3, position: 2.4, shareOfVoice: 25.1, change: 3.1 },
        { brandName: 'ClickUp', visibility: 78.9, position: 2.8, shareOfVoice: 21.4, change: -0.8 },
      ],
      keywords: [
        { keyword: '最佳項目管理工具', volume: 8900, difficulty: 72, position: 2 },
        { keyword: '團隊協作軟體', volume: 5400, difficulty: 68, position: 3 },
        { keyword: '敏捷項目管理', volume: 3200, difficulty: 75, position: 1 },
      ],
      opportunities: [
        '加強移動端功能宣傳',
        '突出整合能力優勢',
        '強調用戶體驗改進'
      ]
    },
    {
      id: '2',
      topic: 'CRM系統比較',
      category: '企業軟體',
      brandPerformance: {
        visibility: 82.7,
        position: 2.3,
        shareOfVoice: 28.9,
        change: -2.1
      },
      competitors: [
        { brandName: 'Salesforce', visibility: 94.8, position: 1.1, shareOfVoice: 42.3, change: 1.8 },
        { brandName: 'HubSpot', visibility: 88.4, position: 1.7, shareOfVoice: 35.6, change: 3.2 },
        { brandName: 'Pipedrive', visibility: 79.2, position: 2.6, shareOfVoice: 24.7, change: -1.2 },
        { brandName: 'Zoho CRM', visibility: 75.8, position: 3.1, shareOfVoice: 19.8, change: 0.5 },
      ],
      keywords: [
        { keyword: '企業CRM系統', volume: 12400, difficulty: 78, position: 2 },
        { keyword: 'CRM軟體比較', volume: 6800, difficulty: 71, position: 3 },
        { keyword: '銷售管理工具', volume: 4500, difficulty: 69, position: 2 },
      ],
      opportunities: [
        '強調性價比優勢',
        '突出本地化服務',
        '加強案例研究展示'
      ]
    },
    {
      id: '3',
      topic: '雲端儲存服務',
      category: '雲端服務',
      brandPerformance: {
        visibility: 91.5,
        position: 1.4,
        shareOfVoice: 41.2,
        change: 7.8
      },
      competitors: [
        { brandName: 'Google Drive', visibility: 95.2, position: 1.0, shareOfVoice: 45.8, change: 2.1 },
        { brandName: 'Dropbox', visibility: 87.9, position: 1.8, shareOfVoice: 32.4, change: -0.9 },
        { brandName: 'OneDrive', visibility: 84.6, position: 2.2, shareOfVoice: 28.7, change: 1.5 },
        { brandName: 'iCloud', visibility: 79.3, position: 2.9, shareOfVoice: 22.1, change: -2.3 },
      ],
      keywords: [
        { keyword: '最佳雲端儲存', volume: 15600, difficulty: 74, position: 1 },
        { keyword: '免費雲端空間', volume: 9200, difficulty: 66, position: 2 },
        { keyword: '企業雲端儲存', volume: 7800, difficulty: 79, position: 1 },
      ],
      opportunities: [
        '擴大免費空間宣傳',
        '強調安全性特色',
        '推廣協作功能'
      ]
    },
  ],
};

export default function TopicAnalysisPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [data, setData] = useState(mockTopicData);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  // 格式化數字
  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('zh-TW').format(num);
  };

  // 獲取變化趨勢圖標和顏色
  const getTrendIcon = (change: number) => {
    if (change > 0) {
      return <ArrowUp className="w-4 h-4 text-green-500" />;
    } else if (change < 0) {
      return <ArrowDown className="w-4 h-4 text-red-500" />;
    }
    return <Minus className="w-4 h-4 text-gray-500" />;
  };

  const getTrendColor = (change: number) => {
    if (change > 0) return 'text-green-600';
    if (change < 0) return 'text-red-600';
    return 'text-gray-600';
  };

  // 獲取可見度等級顏色
  const getVisibilityColor = (visibility: number) => {
    if (visibility >= 90) return 'text-green-600';
    if (visibility >= 80) return 'text-blue-600';
    if (visibility >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  // 獲取關鍵字難度顏色
  const getDifficultyColor = (difficulty: number) => {
    if (difficulty >= 80) return 'bg-red-100 text-red-800';
    if (difficulty >= 60) return 'bg-yellow-100 text-yellow-800';
    return 'bg-green-100 text-green-800';
  };

  // 刷新數據
  const handleRefresh = async () => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  };

  return (
    <div className="space-y-6">
      {/* 頁面標題和操作 */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">主題分析</h2>
          <p className="text-gray-600">分析品牌在特定主題的表現</p>
        </div>
        <div className="flex items-center gap-2">
          <Button onClick={handleRefresh} disabled={isLoading} variant="outline">
            <RefreshCw className={cn('w-4 h-4 mr-2', isLoading && 'animate-spin')} />
            刷新
          </Button>
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            導出
          </Button>
        </div>
      </div>

      {/* 統計概覽 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">總主題數</CardTitle>
            <Hash className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.summary.totalTopics}</div>
            <p className="text-xs text-muted-foreground">
              正在追蹤的主題總數
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">平均可見度</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {data.summary.averageVisibility.toFixed(1)}%
            </div>
            <p className="text-xs text-muted-foreground">
              所有主題的平均可見度
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">表現優異</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {data.summary.topPerformingTopics}
            </div>
            <p className="text-xs text-muted-foreground">
              可見度超過 85% 的主題
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">改進機會</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">
              {data.summary.improvementOpportunities}
            </div>
            <p className="text-xs text-muted-foreground">
              有改進潛力的主題數量
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 篩選和搜索 */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="搜索主題或關鍵字..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select 
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm"
              >
                <option value="all">所有類別</option>
                <option value="生產力軟體">生產力軟體</option>
                <option value="企業軟體">企業軟體</option>
                <option value="雲端服務">雲端服務</option>
                <option value="電商平台">電商平台</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 主題列表 */}
      <div className="space-y-6">
        {data.topics.map((topic) => (
          <Card key={topic.id}>
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <Badge variant="secondary">{topic.category}</Badge>
                    <div className="flex items-center gap-1">
                      {getTrendIcon(topic.brandPerformance.change)}
                      <span className={cn('text-sm font-medium', getTrendColor(topic.brandPerformance.change))}>
                        {Math.abs(topic.brandPerformance.change).toFixed(1)}%
                      </span>
                    </div>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    {topic.topic}
                  </h3>
                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                    <div>
                      <div className="text-sm text-gray-500">可見度</div>
                      <div className={cn('text-lg font-semibold', getVisibilityColor(topic.brandPerformance.visibility))}>
                        {topic.brandPerformance.visibility.toFixed(1)}%
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-500">平均位置</div>
                      <div className="text-lg font-semibold text-gray-900">
                        {topic.brandPerformance.position.toFixed(1)}
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-500">聲量佔比</div>
                      <div className="text-lg font-semibold text-gray-900">
                        {topic.brandPerformance.shareOfVoice.toFixed(1)}%
                      </div>
                    </div>
                  </div>
                </div>
                <Button variant="ghost" size="sm">
                  <MoreHorizontal className="w-4 h-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* 競爭對手表現 */}
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-3">競爭對手表現</h4>
                  <div className="space-y-3">
                    {topic.competitors.map((competitor, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className="flex items-center justify-center w-6 h-6 rounded-full bg-gray-100 text-xs font-semibold">
                            {index + 1}
                          </div>
                          <span className="text-sm font-medium">{competitor.brandName}</span>
                        </div>
                        <div className="flex items-center space-x-4 text-sm">
                          <div className="text-center">
                            <div className={cn('font-semibold', getVisibilityColor(competitor.visibility))}>
                              {competitor.visibility.toFixed(1)}%
                            </div>
                            <div className="text-xs text-gray-500">可見度</div>
                          </div>
                          <div className="text-center">
                            <div className="font-semibold text-gray-900">
                              {competitor.position.toFixed(1)}
                            </div>
                            <div className="text-xs text-gray-500">位置</div>
                          </div>
                          <div className="text-center">
                            <div className="font-semibold text-gray-900">
                              {competitor.shareOfVoice.toFixed(1)}%
                            </div>
                            <div className="text-xs text-gray-500">聲量</div>
                          </div>
                          <div className="flex items-center gap-1">
                            {getTrendIcon(competitor.change)}
                            <span className={cn('text-xs', getTrendColor(competitor.change))}>
                              {Math.abs(competitor.change).toFixed(1)}%
                            </span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* 關鍵字表現 */}
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-3">關鍵字表現</h4>
                  <div className="space-y-2">
                    {topic.keywords.map((keyword, index) => (
                      <div key={index} className="flex items-center justify-between py-2 px-3 bg-gray-50 rounded-lg">
                        <div className="flex-1">
                          <div className="font-medium text-sm">{keyword.keyword}</div>
                          <div className="text-xs text-gray-500">
                            搜索量: {formatNumber(keyword.volume)} | 位置: #{keyword.position}
                          </div>
                        </div>
                        <Badge className={getDifficultyColor(keyword.difficulty)}>
                          難度 {keyword.difficulty}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </div>

                {/* 改進機會 */}
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-3">改進機會</h4>
                  <div className="flex flex-wrap gap-2">
                    {topic.opportunities.map((opportunity, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {opportunity}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* 分頁 */}
      <div className="flex justify-center">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" disabled>
            上一頁
          </Button>
          <span className="text-sm text-gray-600">第 1 頁，共 5 頁</span>
          <Button variant="outline" size="sm">
            下一頁
          </Button>
        </div>
      </div>
    </div>
  );
}
