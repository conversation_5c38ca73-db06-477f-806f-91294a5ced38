'use client';

import React, { useState } from 'react';
import { 
  FileText, 
  BarChart3, 
  TrendingUp, 
  Download,
  Calendar,
  Filter,
  Search,
  Eye,
  Trash2,
  RefreshCw,
  Plus,
  ExternalLink,
  Clock,
  AlertCircle
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

// 報告統計數據
const reportStats = {
  totalReports: 18,
  completedReports: 14,
  processingReports: 2,
  failedReports: 2,
  totalDownloads: 89,
  avgGenerationTime: '3.8 分鐘'
};

// 品牌列表
const brands = [
  { id: '1', name: 'TechCorp Solutions', reportsCount: 6 },
  { id: '2', name: 'HealthPlus Medical', reportsCount: 4 },
  { id: '3', name: 'EcoGreen Energy', reportsCount: 8 }
];

// 報告數據
const allReports = [
  {
    id: '1',
    title: 'TechCorp Solutions - AI可見度分析報告',
    brandName: 'TechCorp Solutions',
    type: 'visibility',
    status: 'completed',
    createdAt: '2024-01-15T10:30:00Z',
    completedAt: '2024-01-15T10:45:00Z',
    downloadCount: 8,
    fileSize: '2.1 MB',
    visibility: 85.2
  },
  {
    id: '2',
    title: 'HealthPlus Medical - 回應分析報告',
    brandName: 'HealthPlus Medical',
    type: 'responses',
    status: 'completed',
    createdAt: '2024-01-14T15:20:00Z',
    completedAt: '2024-01-14T15:35:00Z',
    downloadCount: 12,
    fileSize: '1.9 MB',
    visibility: 78.6
  },
  {
    id: '3',
    title: 'EcoGreen Energy - 引用分析報告',
    brandName: 'EcoGreen Energy',
    type: 'citations',
    status: 'processing',
    createdAt: '2024-01-15T11:00:00Z',
    progress: 70
  },
  {
    id: '4',
    title: 'TechCorp Solutions - 主題分析報告',
    brandName: 'TechCorp Solutions',
    type: 'topics',
    status: 'failed',
    createdAt: '2024-01-13T09:15:00Z',
    error: 'AI API 配額不足'
  },
  {
    id: '5',
    title: 'HealthPlus Medical - 內容分析報告',
    brandName: 'HealthPlus Medical',
    type: 'content',
    status: 'completed',
    createdAt: '2024-01-12T14:30:00Z',
    completedAt: '2024-01-12T14:48:00Z',
    downloadCount: 15,
    fileSize: '2.3 MB',
    visibility: 82.1
  },
  {
    id: '6',
    title: 'EcoGreen Energy - 綜合分析報告',
    brandName: 'EcoGreen Energy',
    type: 'comprehensive',
    status: 'completed',
    createdAt: '2024-01-11T16:00:00Z',
    completedAt: '2024-01-11T16:22:00Z',
    downloadCount: 20,
    fileSize: '3.2 MB',
    visibility: 79.8
  }
];

export default function ProductAnalysisReportsPage() {
  const [selectedBrand, setSelectedBrand] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [selectedType, setSelectedType] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  // 篩選報告
  const filteredReports = allReports.filter(report => {
    const brandMatch = selectedBrand === 'all' || report.brandName === brands.find(b => b.id === selectedBrand)?.name;
    const statusMatch = selectedStatus === 'all' || report.status === selectedStatus;
    const typeMatch = selectedType === 'all' || report.type === selectedType;
    const searchMatch = report.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                       report.brandName.toLowerCase().includes(searchTerm.toLowerCase());
    
    return brandMatch && statusMatch && typeMatch && searchMatch;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'processing':
        return 'bg-blue-100 text-blue-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'completed':
        return '已完成';
      case 'processing':
        return '生成中';
      case 'failed':
        return '失敗';
      default:
        return '未知';
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'comprehensive':
        return '綜合分析';
      case 'visibility':
        return '可見度分析';
      case 'responses':
        return '回應分析';
      case 'citations':
        return '引用分析';
      case 'topics':
        return '主題分析';
      case 'content':
        return '內容分析';
      default:
        return '未知類型';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-TW', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleGenerateReport = (brandId: string, reportType: string) => {
    // 模擬報告生成
    console.log(`生成報告 - 品牌: ${brandId}, 類型: ${reportType}`);
    // 實際實作時會呼叫 API
  };

  const handleDownloadReport = (reportId: string) => {
    // 模擬報告下載
    console.log(`下載報告: ${reportId}`);
    // 實際實作時會下載檔案
  };

  const handleDeleteReport = (reportId: string) => {
    // 模擬報告刪除
    console.log(`刪除報告: ${reportId}`);
    // 實際實作時會呼叫 API
  };

  return (
    <div className="space-y-6">
      {/* 統計概覽 */}
      <div className="grid grid-cols-1 md:grid-cols-6 gap-6">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">總報告數</p>
                <p className="text-2xl font-bold text-gray-900">{reportStats.totalReports}</p>
              </div>
              <FileText className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">已完成</p>
                <p className="text-2xl font-bold text-green-600">{reportStats.completedReports}</p>
              </div>
              <BarChart3 className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">生成中</p>
                <p className="text-2xl font-bold text-blue-600">{reportStats.processingReports}</p>
              </div>
              <Clock className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">失敗</p>
                <p className="text-2xl font-bold text-red-600">{reportStats.failedReports}</p>
              </div>
              <AlertCircle className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">總下載數</p>
                <p className="text-2xl font-bold text-purple-600">{reportStats.totalDownloads}</p>
              </div>
              <Download className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">平均時間</p>
                <p className="text-lg font-bold text-orange-600">{reportStats.avgGenerationTime}</p>
              </div>
              <Calendar className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 主要內容 */}
      <Tabs defaultValue="generate" className="space-y-4">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="generate">生成報告</TabsTrigger>
          <TabsTrigger value="manage">管理報告</TabsTrigger>
        </TabsList>

        <TabsContent value="generate">
          <Card>
            <CardHeader>
              <CardTitle>生成新報告</CardTitle>
              <CardDescription>
                為指定品牌生成 AI 可見度分析報告
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* 品牌選擇 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    選擇品牌
                  </label>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {brands.map(brand => (
                      <Card 
                        key={brand.id} 
                        className="cursor-pointer hover:shadow-md transition-shadow border-2 hover:border-blue-200"
                      >
                        <CardContent className="pt-4">
                          <div className="text-center">
                            <h3 className="font-semibold text-gray-900">{brand.name}</h3>
                            <p className="text-sm text-gray-600 mt-1">
                              {brand.reportsCount} 個報告
                            </p>
                            <Button 
                              size="sm" 
                              className="mt-3"
                              onClick={() => handleGenerateReport(brand.id, 'comprehensive')}
                            >
                              <Plus className="h-4 w-4 mr-1" />
                              生成報告
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>

                {/* 報告類型選擇 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    報告類型
                  </label>
                  <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
                    {[
                      { type: 'comprehensive', label: '綜合分析', description: '完整的品牌可見度分析' },
                      { type: 'visibility', label: '可見度分析', description: 'AI 引擎可見度統計' },
                      { type: 'responses', label: '回應分析', description: 'AI 回應內容分析' },
                      { type: 'citations', label: '引用分析', description: '引用來源分析' },
                      { type: 'topics', label: '主題分析', description: '主題表現分析' },
                      { type: 'content', label: '內容分析', description: '內容優化建議' }
                    ].map(reportType => (
                      <Card key={reportType.type} className="cursor-pointer hover:shadow-md transition-shadow">
                        <CardContent className="pt-4">
                          <div className="text-center">
                            <h4 className="font-medium text-gray-900 text-sm">{reportType.label}</h4>
                            <p className="text-xs text-gray-600 mt-1">{reportType.description}</p>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="manage">
          {/* 篩選和搜尋 */}
          <Card>
            <CardHeader>
              <CardTitle>篩選和搜尋</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    搜尋報告
                  </label>
                  <Input
                    placeholder="搜尋報告標題或品牌..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    品牌篩選
                  </label>
                  <select
                    value={selectedBrand}
                    onChange={(e) => setSelectedBrand(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="all">所有品牌</option>
                    {brands.map(brand => (
                      <option key={brand.id} value={brand.id}>{brand.name}</option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    狀態篩選
                  </label>
                  <select
                    value={selectedStatus}
                    onChange={(e) => setSelectedStatus(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="all">所有狀態</option>
                    <option value="completed">已完成</option>
                    <option value="processing">生成中</option>
                    <option value="failed">失敗</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    類型篩選
                  </label>
                  <select
                    value={selectedType}
                    onChange={(e) => setSelectedType(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="all">所有類型</option>
                    <option value="comprehensive">綜合分析</option>
                    <option value="visibility">可見度分析</option>
                    <option value="responses">回應分析</option>
                    <option value="citations">引用分析</option>
                    <option value="topics">主題分析</option>
                    <option value="content">內容分析</option>
                  </select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 報告列表 */}
          <Card>
            <CardHeader>
              <CardTitle>報告列表</CardTitle>
              <CardDescription>
                管理所有生成的分析報告 ({filteredReports.length} 個報告)
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredReports.map((report) => (
                  <div key={report.id} className="flex items-center justify-between p-4 border rounded-lg hover:shadow-sm transition-shadow">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h4 className="font-medium text-gray-900">{report.title}</h4>
                        <Badge className={getStatusColor(report.status)}>
                          {getStatusLabel(report.status)}
                        </Badge>
                        <Badge variant="outline">
                          {getTypeLabel(report.type)}
                        </Badge>
                      </div>
                      <div className="flex items-center space-x-4 text-sm text-gray-600">
                        <span>品牌: {report.brandName}</span>
                        <span>建立: {formatDate(report.createdAt)}</span>
                        {report.status === 'completed' && (
                          <>
                            <span>大小: {report.fileSize}</span>
                            <span>下載: {report.downloadCount} 次</span>
                            {report.visibility && (
                              <span className="text-green-600">可見度: {report.visibility}%</span>
                            )}
                          </>
                        )}
                        {report.status === 'processing' && (
                          <div className="flex items-center space-x-2">
                            <div className="w-24 bg-gray-200 rounded-full h-2">
                              <div 
                                className="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                                style={{ width: `${report.progress}%` }}
                              ></div>
                            </div>
                            <span>{report.progress}%</span>
                          </div>
                        )}
                        {report.status === 'failed' && (
                          <span className="text-red-600">錯誤: {report.error}</span>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {report.status === 'completed' && (
                        <>
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => handleDownloadReport(report.id)}
                          >
                            <Download className="h-4 w-4 mr-1" />
                            下載
                          </Button>
                          <Button variant="outline" size="sm">
                            <Eye className="h-4 w-4 mr-1" />
                            預覽
                          </Button>
                        </>
                      )}
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="text-red-600 hover:text-red-700"
                        onClick={() => handleDeleteReport(report.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
                
                {filteredReports.length === 0 && (
                  <div className="text-center py-8">
                    <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">沒有找到報告</h3>
                    <p className="text-gray-600">請調整篩選條件或生成新報告</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
} 