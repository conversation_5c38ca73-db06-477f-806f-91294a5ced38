/**
 * 內容分析頁面
 * 優化內容以提升 AI 可見度
 */

'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Progress } from '@/components/ui/progress';
import { 
  Search, 
  Download, 
  FileText, 
  TrendingUp,
  Eye,
  Target,
  RefreshCw,
  ExternalLink,
  CheckCircle,
  AlertCircle,
  XCircle,
  Lightbulb,
  BarChart3
} from 'lucide-react';
import { cn } from '@/lib/utils';

// 模擬內容分析數據
const mockContentData = {
  summary: {
    totalContent: 156,
    averageScore: 78.3,
    optimizedContent: 89,
    needsImprovement: 67,
  },
  content: [
    {
      id: '1',
      url: 'https://example.com/best-project-management-tools-2024',
      title: '2024年最佳項目管理工具完整指南',
      contentType: 'article',
      structure: {
        headings: [
          { level: 1, text: '2024年最佳項目管理工具完整指南', count: 1 },
          { level: 2, text: '什麼是項目管理工具', count: 1 },
          { level: 2, text: '頂級項目管理工具比較', count: 1 },
          { level: 3, text: 'Asana - 團隊協作首選', count: 1 },
          { level: 3, text: 'Trello - 看板式管理', count: 1 },
        ],
        wordCount: 2847,
        readabilityScore: 82.5,
      },
      seoOptimization: {
        score: 85.2,
        metaTitle: '2024年最佳項目管理工具完整指南 | 專業評測',
        metaDescription: '深入比較2024年最受歡迎的項目管理工具，包括Asana、Trello、Monday.com等，幫助您選擇最適合的解決方案。',
        keywords: [
          { keyword: '項目管理工具', density: 2.8, optimal: true },
          { keyword: '團隊協作', density: 1.9, optimal: true },
          { keyword: 'Asana', density: 1.2, optimal: true },
          { keyword: 'Trello', density: 1.1, optimal: true },
        ]
      },
      aiVisibility: {
        citationPotential: 91.3,
        optimizationSuggestions: [
          '增加更多實際使用案例',
          '添加價格比較表格',
          '包含用戶評價和評分',
          '優化移動端體驗描述'
        ]
      },
      lastAnalyzed: '2024-06-24T10:30:00Z',
      status: 'optimized'
    },
    {
      id: '2',
      url: 'https://example.com/crm-systems-comparison-guide',
      title: 'CRM系統比較指南：選擇最適合的客戶管理解決方案',
      contentType: 'article',
      structure: {
        headings: [
          { level: 1, text: 'CRM系統比較指南', count: 1 },
          { level: 2, text: 'CRM系統的重要性', count: 1 },
          { level: 2, text: '主要CRM系統功能比較', count: 1 },
          { level: 3, text: 'Salesforce功能分析', count: 1 },
          { level: 3, text: 'HubSpot特色介紹', count: 1 },
        ],
        wordCount: 3156,
        readabilityScore: 79.8,
      },
      seoOptimization: {
        score: 72.6,
        metaTitle: 'CRM系統比較指南 - 客戶管理解決方案',
        metaDescription: '全面比較主流CRM系統，包括Salesforce、HubSpot、Pipedrive等，幫助企業選擇最佳客戶管理工具。',
        keywords: [
          { keyword: 'CRM系統', density: 3.2, optimal: false },
          { keyword: '客戶管理', density: 2.1, optimal: true },
          { keyword: 'Salesforce', density: 1.8, optimal: true },
          { keyword: 'HubSpot', density: 1.5, optimal: true },
        ]
      },
      aiVisibility: {
        citationPotential: 76.8,
        optimizationSuggestions: [
          '降低CRM系統關鍵字密度',
          '增加更多具體功能比較',
          '添加實施成本分析',
          '包含ROI計算示例'
        ]
      },
      lastAnalyzed: '2024-06-23T14:15:00Z',
      status: 'needs_improvement'
    },
    {
      id: '3',
      url: 'https://example.com/cloud-storage-services-review',
      title: '雲端儲存服務深度評測：安全性與功能全面分析',
      contentType: 'article',
      structure: {
        headings: [
          { level: 1, text: '雲端儲存服務深度評測', count: 1 },
          { level: 2, text: '雲端儲存的安全考量', count: 1 },
          { level: 2, text: '主流服務功能比較', count: 1 },
          { level: 3, text: 'Google Drive完整分析', count: 1 },
          { level: 3, text: 'Dropbox專業評測', count: 1 },
        ],
        wordCount: 2654,
        readabilityScore: 85.1,
      },
      seoOptimization: {
        score: 88.9,
        metaTitle: '雲端儲存服務評測 | 安全性與功能完整分析',
        metaDescription: '專業評測Google Drive、Dropbox、OneDrive等雲端儲存服務，從安全性、功能性、價格等角度全面比較。',
        keywords: [
          { keyword: '雲端儲存', density: 2.5, optimal: true },
          { keyword: '雲端服務', density: 1.7, optimal: true },
          { keyword: 'Google Drive', density: 1.4, optimal: true },
          { keyword: 'Dropbox', density: 1.3, optimal: true },
        ]
      },
      aiVisibility: {
        citationPotential: 93.7,
        optimizationSuggestions: [
          '增加隱私政策比較',
          '添加同步速度測試結果',
          '包含企業級功能對比',
          '優化圖表和視覺元素'
        ]
      },
      lastAnalyzed: '2024-06-22T09:45:00Z',
      status: 'excellent'
    },
  ],
};

export default function ContentAnalysisPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [data, setData] = useState(mockContentData);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedType, setSelectedType] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');

  // 格式化時間
  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleDateString('zh-TW');
  };

  // 格式化數字
  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('zh-TW').format(num);
  };

  // 獲取狀態顏色和圖標
  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'excellent':
        return { 
          color: 'text-green-600 bg-green-50', 
          icon: CheckCircle, 
          text: '優秀' 
        };
      case 'optimized':
        return { 
          color: 'text-blue-600 bg-blue-50', 
          icon: CheckCircle, 
          text: '已優化' 
        };
      case 'needs_improvement':
        return { 
          color: 'text-yellow-600 bg-yellow-50', 
          icon: AlertCircle, 
          text: '需改進' 
        };
      case 'poor':
        return { 
          color: 'text-red-600 bg-red-50', 
          icon: XCircle, 
          text: '待優化' 
        };
      default:
        return { 
          color: 'text-gray-600 bg-gray-50', 
          icon: AlertCircle, 
          text: '未知' 
        };
    }
  };

  // 獲取分數顏色
  const getScoreColor = (score: number) => {
    if (score >= 85) return 'text-green-600';
    if (score >= 70) return 'text-blue-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  // 獲取關鍵字密度狀態
  const getKeywordStatus = (optimal: boolean) => {
    return optimal ? 
      { color: 'text-green-600', text: '最佳' } : 
      { color: 'text-yellow-600', text: '需調整' };
  };

  // 刷新數據
  const handleRefresh = async () => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  };

  return (
    <div className="space-y-6">
      {/* 頁面標題和操作 */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">內容分析</h2>
          <p className="text-gray-600">優化內容以提升 AI 可見度</p>
        </div>
        <div className="flex items-center gap-2">
          <Button onClick={handleRefresh} disabled={isLoading} variant="outline">
            <RefreshCw className={cn('w-4 h-4 mr-2', isLoading && 'animate-spin')} />
            刷新
          </Button>
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            導出
          </Button>
        </div>
      </div>

      {/* 統計概覽 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">總內容數</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.summary.totalContent}</div>
            <p className="text-xs text-muted-foreground">
              已分析的內容總數
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">平均分數</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {data.summary.averageScore.toFixed(1)}
            </div>
            <p className="text-xs text-muted-foreground">
              內容優化平均分數
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">已優化</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {data.summary.optimizedContent}
            </div>
            <p className="text-xs text-muted-foreground">
              已完成優化的內容數
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">待改進</CardTitle>
            <AlertCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">
              {data.summary.needsImprovement}
            </div>
            <p className="text-xs text-muted-foreground">
              需要改進的內容數
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 篩選和搜索 */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="搜索標題、URL或關鍵字..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select 
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm"
              >
                <option value="all">所有類型</option>
                <option value="article">文章</option>
                <option value="product">產品頁</option>
                <option value="landing">著陸頁</option>
                <option value="blog">部落格</option>
              </select>
              <select 
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm"
              >
                <option value="all">所有狀態</option>
                <option value="excellent">優秀</option>
                <option value="optimized">已優化</option>
                <option value="needs_improvement">需改進</option>
                <option value="poor">待優化</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 內容列表 */}
      <div className="space-y-6">
        {data.content.map((content) => {
          const statusInfo = getStatusInfo(content.status);
          const StatusIcon = statusInfo.icon;
          
          return (
            <Card key={content.id}>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <Badge variant="secondary">{content.contentType}</Badge>
                      <Badge className={statusInfo.color}>
                        <StatusIcon className="w-3 h-3 mr-1" />
                        {statusInfo.text}
                      </Badge>
                      <span className="text-sm text-gray-500">
                        最後分析: {formatTime(content.lastAnalyzed)}
                      </span>
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      {content.title}
                    </h3>
                    <div className="text-sm text-blue-600 hover:text-blue-800 break-all">
                      {content.url}
                    </div>
                  </div>
                  <Button variant="ghost" size="sm">
                    <ExternalLink className="w-4 h-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {/* 內容結構 */}
                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                    <div>
                      <div className="text-sm text-gray-500">字數統計</div>
                      <div className="text-lg font-semibold text-gray-900">
                        {formatNumber(content.structure.wordCount)}
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-500">可讀性分數</div>
                      <div className={cn('text-lg font-semibold', getScoreColor(content.structure.readabilityScore))}>
                        {content.structure.readabilityScore.toFixed(1)}
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-500">引用潛力</div>
                      <div className={cn('text-lg font-semibold', getScoreColor(content.aiVisibility.citationPotential))}>
                        {content.aiVisibility.citationPotential.toFixed(1)}%
                      </div>
                    </div>
                  </div>

                  {/* SEO 優化分數 */}
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="text-sm font-medium text-gray-900">SEO 優化分數</h4>
                      <span className={cn('text-lg font-semibold', getScoreColor(content.seoOptimization.score))}>
                        {content.seoOptimization.score.toFixed(1)}
                      </span>
                    </div>
                    <Progress value={content.seoOptimization.score} className="h-2" />
                  </div>

                  {/* Meta 信息 */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                    <div>
                      <h4 className="text-sm font-medium text-gray-900 mb-2">Meta 標題</h4>
                      <div className="bg-gray-50 rounded-lg p-3 text-sm">
                        {content.seoOptimization.metaTitle}
                      </div>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-gray-900 mb-2">Meta 描述</h4>
                      <div className="bg-gray-50 rounded-lg p-3 text-sm">
                        {content.seoOptimization.metaDescription}
                      </div>
                    </div>
                  </div>

                  {/* 關鍵字分析 */}
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 mb-3">關鍵字分析</h4>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                      {content.seoOptimization.keywords.map((keyword, index) => {
                        const keywordStatus = getKeywordStatus(keyword.optimal);
                        return (
                          <div key={index} className="flex items-center justify-between py-2 px-3 bg-gray-50 rounded-lg">
                            <div>
                              <div className="font-medium text-sm">{keyword.keyword}</div>
                              <div className="text-xs text-gray-500">
                                密度: {keyword.density.toFixed(1)}%
                              </div>
                            </div>
                            <Badge className={keywordStatus.color}>
                              {keywordStatus.text}
                            </Badge>
                          </div>
                        );
                      })}
                    </div>
                  </div>

                  {/* 優化建議 */}
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 mb-3">
                      <Lightbulb className="w-4 h-4 inline mr-1" />
                      優化建議
                    </h4>
                    <div className="space-y-2">
                      {content.aiVisibility.optimizationSuggestions.map((suggestion, index) => (
                        <div key={index} className="flex items-start gap-2 text-sm">
                          <div className="w-1.5 h-1.5 rounded-full bg-blue-500 mt-2 flex-shrink-0"></div>
                          <span className="text-gray-700">{suggestion}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* 分頁 */}
      <div className="flex justify-center">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" disabled>
            上一頁
          </Button>
          <span className="text-sm text-gray-600">第 1 頁，共 12 頁</span>
          <Button variant="outline" size="sm">
            下一頁
          </Button>
        </div>
      </div>
    </div>
  );
}
