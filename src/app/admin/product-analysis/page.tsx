/**
 * 產品分析主儀表板頁面
 * 顯示品牌可見度總覽和關鍵指標
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  TrendingUp, 
  TrendingDown, 
  Eye, 
  MessageSquare, 
  Link as LinkIcon, 
  BarChart3,
  RefreshCw,
  Download,
  Plus,
  Search
} from 'lucide-react';
import { cn } from '@/lib/utils';

// 模擬數據 - 實際使用時會從 API 獲取
const mockDashboardData = {
  overview: {
    totalBrands: 12,
    totalAnalyses: 248,
    averageVisibility: 78.5,
    activeReports: 6,
    lastUpdated: new Date().toISOString(),
  },
  visibilityTrends: [
    { date: '2024-01', visibility: 72.3, change: 2.1 },
    { date: '2024-02', visibility: 75.8, change: 3.5 },
    { date: '2024-03', visibility: 78.5, change: 2.7 },
    { date: '2024-04', visibility: 76.2, change: -2.3 },
    { date: '2024-05', visibility: 81.4, change: 5.2 },
    { date: '2024-06', visibility: 78.5, change: -2.9 },
  ],
  aiEngineDistribution: [
    { engine: 'ChatGPT', visibility: 85.2, share: 28.5 },
    { engine: 'Gemini', visibility: 79.8, share: 24.3 },
    { engine: 'Perplexity', visibility: 76.4, share: 22.1 },
    { engine: 'Copilot', visibility: 72.1, share: 15.8 },
    { engine: 'Claude', visibility: 68.9, share: 9.3 },
  ],
  topBrands: [
    { name: 'TechCorp', visibility: 92.3, change: 5.2, industry: '科技' },
    { name: 'HealthPlus', visibility: 88.7, change: -1.8, industry: '醫療' },
    { name: 'EcoGreen', visibility: 85.1, change: 3.4, industry: '環保' },
    { name: 'FinanceMax', visibility: 82.9, change: 2.1, industry: '金融' },
    { name: 'EduSmart', visibility: 79.6, change: -0.5, industry: '教育' },
  ],
  recentAnalyses: [
    { id: '1', brand: 'TechCorp', type: '回應分析', status: '完成', date: '2024-06-24' },
    { id: '2', brand: 'HealthPlus', type: '引用分析', status: '進行中', date: '2024-06-24' },
    { id: '3', brand: 'EcoGreen', type: '主題分析', status: '完成', date: '2024-06-23' },
    { id: '4', brand: 'FinanceMax', type: '內容分析', status: '排隊中', date: '2024-06-23' },
  ],
};

export default function ProductAnalysisDashboard() {
  const [isLoading, setIsLoading] = useState(false);
  const [data, setData] = useState(mockDashboardData);

  // 刷新數據
  const handleRefresh = async () => {
    setIsLoading(true);
    // 模擬 API 調用
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  };

  // 格式化數字
  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('zh-TW').format(num);
  };

  // 格式化百分比
  const formatPercentage = (num: number) => {
    return `${num.toFixed(1)}%`;
  };

  // 獲取變化趨勢圖標和顏色
  const getTrendIcon = (change: number) => {
    if (change > 0) {
      return <TrendingUp className="w-4 h-4 text-green-500" />;
    } else if (change < 0) {
      return <TrendingDown className="w-4 h-4 text-red-500" />;
    }
    return null;
  };

  const getTrendColor = (change: number) => {
    if (change > 0) return 'text-green-600';
    if (change < 0) return 'text-red-600';
    return 'text-gray-600';
  };

  return (
    <div className="space-y-6">
      {/* 頁面操作 */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center gap-4">
          <Button onClick={handleRefresh} disabled={isLoading} variant="outline">
            <RefreshCw className={cn('w-4 h-4 mr-2', isLoading && 'animate-spin')} />
            刷新數據
          </Button>
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            導出報告
          </Button>
        </div>
        <div className="flex items-center gap-2">
          <Button>
            <Plus className="w-4 h-4 mr-2" />
            新增品牌
          </Button>
          <Button variant="outline">
            <Search className="w-4 h-4 mr-2" />
            開始分析
          </Button>
        </div>
      </div>

      {/* 關鍵指標卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">總品牌數</CardTitle>
            <Eye className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(data.overview.totalBrands)}</div>
            <p className="text-xs text-muted-foreground">
              管理中的品牌總數
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">總分析次數</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(data.overview.totalAnalyses)}</div>
            <p className="text-xs text-muted-foreground">
              本月執行的分析總數
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">平均可見度</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {formatPercentage(data.overview.averageVisibility)}
            </div>
            <p className="text-xs text-muted-foreground">
              所有品牌的平均可見度
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">活躍報告</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(data.overview.activeReports)}</div>
            <p className="text-xs text-muted-foreground">
              正在生成或待處理的報告
            </p>
          </CardContent>
        </Card>
      </div>

      {/* AI 引擎分佈和品牌排行 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* AI 引擎可見度分佈 */}
        <Card>
          <CardHeader>
            <CardTitle>AI 引擎可見度分佈</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {data.aiEngineDistribution.map((engine, index) => (
                <div key={engine.engine} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={cn(
                      'w-3 h-3 rounded-full',
                      index === 0 && 'bg-blue-500',
                      index === 1 && 'bg-green-500',
                      index === 2 && 'bg-yellow-500',
                      index === 3 && 'bg-purple-500',
                      index === 4 && 'bg-red-500'
                    )} />
                    <span className="text-sm font-medium">{engine.engine}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-600">
                      {formatPercentage(engine.visibility)}
                    </span>
                    <Badge variant="secondary" className="text-xs">
                      {formatPercentage(engine.share)}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* 品牌可見度排行 */}
        <Card>
          <CardHeader>
            <CardTitle>品牌可見度排行</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {data.topBrands.map((brand, index) => (
                <div key={brand.name} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center justify-center w-6 h-6 rounded-full bg-gray-100 text-xs font-semibold">
                      {index + 1}
                    </div>
                    <div>
                      <div className="text-sm font-medium">{brand.name}</div>
                      <div className="text-xs text-gray-500">{brand.industry}</div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-semibold">
                      {formatPercentage(brand.visibility)}
                    </span>
                    <div className="flex items-center space-x-1">
                      {getTrendIcon(brand.change)}
                      <span className={cn('text-xs', getTrendColor(brand.change))}>
                        {Math.abs(brand.change).toFixed(1)}%
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 最近分析活動 */}
      <Card>
        <CardHeader>
          <CardTitle>最近分析活動</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {data.recentAnalyses.map((analysis) => (
              <div key={analysis.id} className="flex items-center justify-between py-2">
                <div className="flex items-center space-x-4">
                  <div>
                    <div className="text-sm font-medium">{analysis.brand}</div>
                    <div className="text-xs text-gray-500">{analysis.type}</div>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <Badge 
                    variant={
                      analysis.status === '完成' ? 'default' :
                      analysis.status === '進行中' ? 'secondary' : 'outline'
                    }
                  >
                    {analysis.status}
                  </Badge>
                  <span className="text-xs text-gray-500">{analysis.date}</span>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
