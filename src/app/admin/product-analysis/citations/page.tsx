/**
 * 引用分析頁面
 * 分析引用來源的權威性和影響力
 */

'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  Search, 
  Download, 
  Link as LinkIcon, 
  TrendingUp,
  Globe,
  Calendar,
  RefreshCw,
  ExternalLink,
  BarChart3,
  Shield,
  Star
} from 'lucide-react';
import { cn } from '@/lib/utils';

// 模擬引用分析數據
const mockCitationData = {
  summary: {
    totalCitations: 2847,
    uniqueDomains: 156,
    averageAuthority: 72.3,
    citationGrowth: 12.5,
  },
  citations: [
    {
      id: '1',
      url: 'https://techcrunch.com/2024/06/best-project-management-tools',
      title: '2024年最佳項目管理工具完整指南',
      domain: 'techcrunch.com',
      authority: 94.2,
      relevance: 89.7,
      brandMentions: 3,
      citationFrequency: 28,
      lastCited: '2024-06-24T08:30:00Z',
      contentQuality: {
        score: 92.5,
        factors: ['高質量內容', '權威來源', '最新更新', '詳細分析']
      },
      seoMetrics: {
        domainAuthority: 94,
        pageAuthority: 87,
        backlinks: 15420
      }
    },
    {
      id: '2',
      url: 'https://www.forbes.com/sites/forbestechcouncil/2024/06/crm-systems-comparison',
      title: 'CRM系統深度比較：企業級解決方案分析',
      domain: 'forbes.com',
      authority: 96.8,
      relevance: 91.2,
      brandMentions: 5,
      citationFrequency: 42,
      lastCited: '2024-06-23T14:15:00Z',
      contentQuality: {
        score: 95.1,
        factors: ['專家觀點', '數據支持', '行業權威', '全面覆蓋']
      },
      seoMetrics: {
        domainAuthority: 97,
        pageAuthority: 89,
        backlinks: 28750
      }
    },
    {
      id: '3',
      url: 'https://www.pcmag.com/picks/best-cloud-storage-services',
      title: '最佳雲端儲存服務評測與推薦',
      domain: 'pcmag.com',
      authority: 88.5,
      relevance: 85.3,
      brandMentions: 4,
      citationFrequency: 19,
      lastCited: '2024-06-22T11:45:00Z',
      contentQuality: {
        score: 87.8,
        factors: ['詳細測試', '客觀評價', '用戶體驗', '技術分析']
      },
      seoMetrics: {
        domainAuthority: 89,
        pageAuthority: 82,
        backlinks: 9340
      }
    },
    {
      id: '4',
      url: 'https://blog.hubspot.com/marketing/ecommerce-platforms-guide',
      title: '電商平台選擇指南：功能與成本分析',
      domain: 'hubspot.com',
      authority: 91.7,
      relevance: 88.9,
      brandMentions: 6,
      citationFrequency: 35,
      lastCited: '2024-06-21T16:20:00Z',
      contentQuality: {
        score: 90.3,
        factors: ['實用指南', '案例研究', '專業建議', '市場洞察']
      },
      seoMetrics: {
        domainAuthority: 92,
        pageAuthority: 85,
        backlinks: 18650
      }
    },
  ],
};

export default function CitationAnalysisPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [data, setData] = useState(mockCitationData);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState('authority');

  // 格式化時間
  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleDateString('zh-TW');
  };

  // 格式化數字
  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('zh-TW').format(num);
  };

  // 獲取權威性等級顏色
  const getAuthorityColor = (authority: number) => {
    if (authority >= 90) return 'text-green-600 bg-green-50';
    if (authority >= 80) return 'text-blue-600 bg-blue-50';
    if (authority >= 70) return 'text-yellow-600 bg-yellow-50';
    return 'text-gray-600 bg-gray-50';
  };

  // 獲取權威性等級文字
  const getAuthorityLevel = (authority: number) => {
    if (authority >= 90) return '極高';
    if (authority >= 80) return '高';
    if (authority >= 70) return '中';
    return '低';
  };

  // 刷新數據
  const handleRefresh = async () => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  };

  return (
    <div className="space-y-6">
      {/* 頁面標題和操作 */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">引用分析</h2>
          <p className="text-gray-600">分析引用來源的權威性和影響力</p>
        </div>
        <div className="flex items-center gap-2">
          <Button onClick={handleRefresh} disabled={isLoading} variant="outline">
            <RefreshCw className={cn('w-4 h-4 mr-2', isLoading && 'animate-spin')} />
            刷新
          </Button>
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            導出
          </Button>
        </div>
      </div>

      {/* 統計概覽 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">總引用數</CardTitle>
            <LinkIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(data.summary.totalCitations)}</div>
            <p className="text-xs text-muted-foreground">
              累計引用次數
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">獨特域名</CardTitle>
            <Globe className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(data.summary.uniqueDomains)}</div>
            <p className="text-xs text-muted-foreground">
              引用來源域名數量
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">平均權威性</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {data.summary.averageAuthority.toFixed(1)}
            </div>
            <p className="text-xs text-muted-foreground">
              引用來源平均權威分數
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">增長率</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              +{data.summary.citationGrowth.toFixed(1)}%
            </div>
            <p className="text-xs text-muted-foreground">
              本月引用增長率
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 篩選和搜索 */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="搜索域名、標題或內容..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select 
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm"
              >
                <option value="authority">權威性排序</option>
                <option value="relevance">相關性排序</option>
                <option value="frequency">引用頻率排序</option>
                <option value="recent">最近引用排序</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 引用列表 */}
      <div className="space-y-4">
        {data.citations.map((citation) => (
          <Card key={citation.id}>
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <Badge className={getAuthorityColor(citation.authority)}>
                      權威性: {getAuthorityLevel(citation.authority)} ({citation.authority.toFixed(1)})
                    </Badge>
                    <Badge variant="outline">
                      相關性: {citation.relevance.toFixed(1)}%
                    </Badge>
                    <Badge variant="secondary">
                      {citation.domain}
                    </Badge>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    {citation.title}
                  </h3>
                  <div className="flex items-center gap-4 text-sm text-gray-500">
                    <span className="flex items-center gap-1">
                      <LinkIcon className="w-4 h-4" />
                      引用 {citation.citationFrequency} 次
                    </span>
                    <span className="flex items-center gap-1">
                      <Calendar className="w-4 h-4" />
                      最後引用: {formatTime(citation.lastCited)}
                    </span>
                    <span className="flex items-center gap-1">
                      <BarChart3 className="w-4 h-4" />
                      品牌提及 {citation.brandMentions} 次
                    </span>
                  </div>
                </div>
                <Button variant="ghost" size="sm">
                  <ExternalLink className="w-4 h-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* URL */}
                <div className="bg-gray-50 rounded-lg p-3">
                  <a 
                    href={citation.url} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-sm text-blue-600 hover:text-blue-800 break-all"
                  >
                    {citation.url}
                  </a>
                </div>

                {/* 內容質量指標 */}
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="text-sm font-medium text-gray-900">內容質量</h4>
                    <div className="flex items-center gap-1">
                      <Star className="w-4 h-4 text-yellow-500" />
                      <span className="text-sm font-semibold">{citation.contentQuality.score.toFixed(1)}</span>
                    </div>
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {citation.contentQuality.factors.map((factor, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {factor}
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* SEO 指標 */}
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 pt-4 border-t border-gray-100">
                  <div className="text-center">
                    <div className="text-lg font-semibold text-gray-900">
                      {citation.seoMetrics.domainAuthority}
                    </div>
                    <div className="text-xs text-gray-500">域名權威</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-semibold text-gray-900">
                      {citation.seoMetrics.pageAuthority}
                    </div>
                    <div className="text-xs text-gray-500">頁面權威</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-semibold text-gray-900">
                      {formatNumber(citation.seoMetrics.backlinks)}
                    </div>
                    <div className="text-xs text-gray-500">反向連結</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* 分頁 */}
      <div className="flex justify-center">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" disabled>
            上一頁
          </Button>
          <span className="text-sm text-gray-600">第 1 頁，共 8 頁</span>
          <Button variant="outline" size="sm">
            下一頁
          </Button>
        </div>
      </div>
    </div>
  );
}
