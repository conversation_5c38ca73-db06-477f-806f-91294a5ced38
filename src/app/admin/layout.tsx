'use client';

import type { Metadata } from 'next';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Shield, Link as LinkIcon, BarChart3, Settings, Database, Activity, Key, Search, TrendingUp, Target, Users, FileText, Gauge, Eye, Zap, Heart, LogOut, User, MessageSquare, Hash } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/AuthContext';

// 由於使用了 'use client'，metadata 需要在父組件中設置

const adminNavItems = [
  {
    href: '/admin/link-health',
    label: '連結健康度',
    icon: LinkIcon,
    description: '監控網站連結狀態'
  },
  {
    href: '/admin/cache',
    label: '快取管理',
    icon: Database,
    description: '管理連結驗證快取'
  },
  {
    href: '/admin/system',
    label: '系統監控',
    icon: Activity,
    description: '監控系統性能狀態'
  },
  {
    href: '/admin/analytics',
    label: '分析報告',
    icon: BarChart3,
    description: '查看詳細分析報告'
  },
  {
    href: '/admin/settings',
    label: '系統設置',
    icon: Settings,
    description: '配置系統參數'
  },
  {
    href: '/admin/settings/openai',
    label: 'OpenAI 設置',
    icon: Key,
    description: '配置 OpenAI API 設置'
  },
  {
    href: '/admin/product-research',
    label: '產品研究',
    icon: Search,
    description: '品牌可見度和競爭分析'
  },
  {
    href: '/admin/product-research/brands',
    label: '品牌管理',
    icon: Target,
    description: '管理品牌和競爭對手'
  },
  {
    href: '/admin/product-research/analytics',
    label: '研究分析',
    icon: TrendingUp,
    description: '查詢智能和主題分析'
  },
  {
    href: '/admin/product-research/segments',
    label: '戰略分割',
    icon: Users,
    description: '受眾和地區分析'
  },
  {
    href: '/admin/product-research/reports',
    label: '分析報告',
    icon: FileText,
    description: '生成和管理分析報告'
  },
  {
    href: '/admin/product-research/settings',
    label: '研究設定',
    icon: Settings,
    description: '配置產品研究模組'
  },
  {
    href: '/admin/measure',
    label: '測量功能',
    icon: Gauge,
    description: 'AI 搜尋引擎品牌可見度測量'
  },
  {
    href: '/admin/measure/visibility',
    label: '品牌可見度',
    icon: Eye,
    description: '測量品牌在 AI 搜尋引擎的可見度'
  },
  {
    href: '/admin/measure/competitive',
    label: '競爭分析',
    icon: Zap,
    description: '競爭對手定位和基準比較'
  },
  {
    href: '/admin/measure/journey',
    label: '購買旅程',
    icon: TrendingUp,
    description: '分析不同人群的購買旅程'
  },
  {
    href: '/admin/measure/sentiment',
    label: '情感分析',
    icon: Heart,
    description: '品牌情感監控和準確性檢測'
  },
  {
    href: '/admin/product-analysis',
    label: '產品分析',
    icon: TrendingUp,
    description: 'AI 搜尋引擎品牌可見度分析'
  },
  {
    href: '/admin/product-analysis/responses',
    label: '回應分析',
    icon: MessageSquare,
    description: '分析 AI 引擎回應中的品牌提及'
  },
  {
    href: '/admin/product-analysis/citations',
    label: '引用分析',
    icon: LinkIcon,
    description: '分析引用來源的權威性和影響力'
  },
  {
    href: '/admin/product-analysis/topics',
    label: '主題分析',
    icon: Hash,
    description: '分析品牌在特定主題的表現'
  },
  {
    href: '/admin/product-analysis/content',
    label: '內容分析',
    icon: FileText,
    description: '優化內容以提升 AI 可見度'
  }
];

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const router = useRouter();
  const { user, isAuthenticated, isLoading, logout, hasRole } = useAuth();

  useEffect(() => {
    console.log('🔍 管理員布局權限檢查:', {
      isLoading,
      isAuthenticated,
      user: user?.email,
      userRole: user?.role
    });

    // 檢查認證狀態
    if (!isLoading && !isAuthenticated) {
      console.log('❌ 未認證，重定向到登入頁面');
      toast.error('請先登入');
      router.push('/auth/login');
      return;
    }

    // 檢查管理員權限 - 添加更詳細的日誌
    if (!isLoading && isAuthenticated && user) {
      const isAdmin = hasRole('admin');
      console.log('👑 權限檢查結果:', {
        userRole: user.role,
        hasAdminRole: isAdmin,
        hasRoleFunction: typeof hasRole
      });

      if (!isAdmin) {
        console.log('❌ 權限不足，需要管理員權限');
        toast.error('權限不足，需要管理員權限');
        router.push('/auth/login');
        return;
      }

      console.log('✅ 管理員權限驗證通過');
    }
  }, [isAuthenticated, isLoading, hasRole, router, user]);

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      toast.error('登出時發生錯誤');
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">載入中...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 管理後台頭部 */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Shield className="h-8 w-8 text-blue-600 mr-3" />
              <div>
                <h1 className="text-xl font-semibold text-gray-900">管理後台</h1>
                <p className="text-sm text-gray-500">AI SEO 優化王</p>
              </div>
            </div>
            
            <nav className="flex items-center space-x-4">
              <Link
                href="/"
                className="text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium"
              >
                返回首頁
              </Link>

              {/* 用戶下拉選單 */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="flex items-center space-x-2">
                    <User className="h-4 w-4" />
                    <span className="hidden sm:inline">{user?.name || user?.email}</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <DropdownMenuLabel>我的帳號</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem disabled>
                    <User className="mr-2 h-4 w-4" />
                    <div>
                      <div className="font-medium">{user?.name || user?.email}</div>
                      <div className="text-sm text-gray-500">{user?.email}</div>
                    </div>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleLogout} className="text-red-600">
                    <LogOut className="mr-2 h-4 w-4" />
                    登出
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </nav>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex gap-8">
          {/* 側邊導航 */}
          <aside className="w-64 flex-shrink-0">
            <nav className="bg-white rounded-lg shadow p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">導航</h2>
              <ul className="space-y-2">
                {adminNavItems.map((item) => {
                  const Icon = item.icon;
                  return (
                    <li key={item.href}>
                      <Link
                        href={item.href}
                        className="flex items-center p-3 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors group"
                      >
                        <Icon className="h-5 w-5 mr-3 text-gray-400 group-hover:text-gray-600" />
                        <div>
                          <div className="font-medium">{item.label}</div>
                          <div className="text-sm text-gray-500">{item.description}</div>
                        </div>
                      </Link>
                    </li>
                  );
                })}
              </ul>
            </nav>
          </aside>

          {/* 主要內容區域 */}
          <main className="flex-1">
            {children}
          </main>
        </div>
      </div>
    </div>
  );
}
