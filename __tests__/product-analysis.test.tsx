/**
 * 產品分析模組測試文件
 * 測試產品分析功能的核心組件和 API
 */

import { describe, it, expect, jest, beforeEach } from '@jest/globals';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    prefetch: jest.fn(),
  }),
  usePathname: () => '/admin/product-analysis',
}));

// Mock fetch
global.fetch = jest.fn();

// Mock data
const mockDashboardData = {
  success: true,
  data: {
    overview: {
      totalBrands: 12,
      totalAnalyses: 248,
      averageVisibility: 78.5,
      activeReports: 6,
      lastUpdated: '2024-06-24T10:30:00Z',
    },
    aiEngineDistribution: [
      { engine: 'ChatGPT', visibility: 85.2, share: 28.5 },
      { engine: 'Gemini', visibility: 79.8, share: 24.3 },
    ],
    topBrands: [
      { name: 'TechCorp', visibility: 92.3, change: 5.2, industry: '科技' },
      { name: 'HealthPlus', visibility: 88.7, change: -1.8, industry: '醫療' },
    ],
    recentAnalyses: [
      { id: '1', brand: 'TechCorp', type: '回應分析', status: '完成', date: '2024-06-24' },
    ],
  },
};

const mockResponseData = {
  success: true,
  data: {
    summary: {
      totalResponses: 1247,
      brandMentions: 892,
      averagePosition: 2.3,
      sentimentScore: 78.5,
    },
    responses: [
      {
        id: '1',
        query: '最佳項目管理工具推薦',
        aiEngine: 'ChatGPT',
        response: '對於項目管理，我推薦幾個優秀的工具...',
        brandMentions: [
          { brand: 'Asana', position: 1, prominence: 'high', sentiment: 'positive' },
        ],
        timestamp: '2024-06-24T10:30:00Z',
        relevanceScore: 92.5,
        sentiment: 'positive',
        position: 1,
      },
    ],
  },
};

describe('產品分析模組', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('API 端點測試', () => {
    it('應該成功獲取產品分析儀表板數據', async () => {
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockDashboardData,
      });

      const response = await fetch('/api/product-analysis');
      const data = await response.json();

      expect(fetch).toHaveBeenCalledWith('/api/product-analysis');
      expect(data.success).toBe(true);
      expect(data.data.overview.totalBrands).toBe(12);
      expect(data.data.aiEngineDistribution).toHaveLength(2);
    });

    it('應該成功獲取回應分析數據', async () => {
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponseData,
      });

      const response = await fetch('/api/product-analysis/responses?section=summary');
      const data = await response.json();

      expect(fetch).toHaveBeenCalledWith('/api/product-analysis/responses?section=summary');
      expect(data.success).toBe(true);
      expect(data.data.summary.totalResponses).toBe(1247);
    });

    it('應該處理 API 錯誤', async () => {
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        json: async () => ({
          success: false,
          error: { code: 'INTERNAL_ERROR', message: '服務器錯誤' },
        }),
      });

      const response = await fetch('/api/product-analysis');
      const data = await response.json();

      expect(data.success).toBe(false);
      expect(data.error.code).toBe('INTERNAL_ERROR');
    });
  });

  describe('數據格式驗證', () => {
    it('儀表板數據應該包含必要字段', () => {
      const { overview, aiEngineDistribution, topBrands } = mockDashboardData.data;

      // 檢查概覽數據
      expect(overview).toHaveProperty('totalBrands');
      expect(overview).toHaveProperty('totalAnalyses');
      expect(overview).toHaveProperty('averageVisibility');
      expect(overview).toHaveProperty('activeReports');

      // 檢查 AI 引擎分佈數據
      expect(Array.isArray(aiEngineDistribution)).toBe(true);
      aiEngineDistribution.forEach(engine => {
        expect(engine).toHaveProperty('engine');
        expect(engine).toHaveProperty('visibility');
        expect(engine).toHaveProperty('share');
      });

      // 檢查品牌排行數據
      expect(Array.isArray(topBrands)).toBe(true);
      topBrands.forEach(brand => {
        expect(brand).toHaveProperty('name');
        expect(brand).toHaveProperty('visibility');
        expect(brand).toHaveProperty('change');
        expect(brand).toHaveProperty('industry');
      });
    });

    it('回應分析數據應該包含必要字段', () => {
      const { summary, responses } = mockResponseData.data;

      // 檢查摘要數據
      expect(summary).toHaveProperty('totalResponses');
      expect(summary).toHaveProperty('brandMentions');
      expect(summary).toHaveProperty('averagePosition');
      expect(summary).toHaveProperty('sentimentScore');

      // 檢查回應數據
      expect(Array.isArray(responses)).toBe(true);
      responses.forEach(response => {
        expect(response).toHaveProperty('id');
        expect(response).toHaveProperty('query');
        expect(response).toHaveProperty('aiEngine');
        expect(response).toHaveProperty('brandMentions');
        expect(response).toHaveProperty('sentiment');
        expect(response).toHaveProperty('relevanceScore');
      });
    });
  });

  describe('數據處理函數', () => {
    it('應該正確格式化數字', () => {
      const formatNumber = (num: number) => new Intl.NumberFormat('zh-TW').format(num);
      
      expect(formatNumber(1234)).toBe('1,234');
      expect(formatNumber(1234567)).toBe('1,234,567');
    });

    it('應該正確格式化百分比', () => {
      const formatPercentage = (num: number) => `${num.toFixed(1)}%`;
      
      expect(formatPercentage(78.5)).toBe('78.5%');
      expect(formatPercentage(92.34567)).toBe('92.3%');
    });

    it('應該正確判斷趨勢方向', () => {
      const getTrendDirection = (change: number) => {
        if (change > 0) return 'up';
        if (change < 0) return 'down';
        return 'neutral';
      };

      expect(getTrendDirection(5.2)).toBe('up');
      expect(getTrendDirection(-1.8)).toBe('down');
      expect(getTrendDirection(0)).toBe('neutral');
    });
  });

  describe('篩選和排序功能', () => {
    const sampleData = [
      { name: 'A', visibility: 90, change: 5 },
      { name: 'B', visibility: 80, change: -2 },
      { name: 'C', visibility: 95, change: 1 },
    ];

    it('應該正確按可見度排序', () => {
      const sorted = [...sampleData].sort((a, b) => b.visibility - a.visibility);
      
      expect(sorted[0].name).toBe('C');
      expect(sorted[1].name).toBe('A');
      expect(sorted[2].name).toBe('B');
    });

    it('應該正確按變化率排序', () => {
      const sorted = [...sampleData].sort((a, b) => b.change - a.change);
      
      expect(sorted[0].name).toBe('A');
      expect(sorted[1].name).toBe('C');
      expect(sorted[2].name).toBe('B');
    });

    it('應該正確篩選高可見度項目', () => {
      const filtered = sampleData.filter(item => item.visibility >= 90);
      
      expect(filtered).toHaveLength(2);
      expect(filtered.map(item => item.name)).toEqual(['A', 'C']);
    });
  });

  describe('錯誤處理', () => {
    it('應該處理網絡錯誤', async () => {
      (global.fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'));

      try {
        await fetch('/api/product-analysis');
      } catch (error) {
        expect(error).toBeInstanceOf(Error);
        expect((error as Error).message).toBe('Network error');
      }
    });

    it('應該處理無效的 JSON 響應', async () => {
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => {
          throw new Error('Invalid JSON');
        },
      });

      try {
        const response = await fetch('/api/product-analysis');
        await response.json();
      } catch (error) {
        expect(error).toBeInstanceOf(Error);
        expect((error as Error).message).toBe('Invalid JSON');
      }
    });
  });

  describe('分頁功能', () => {
    it('應該正確計算分頁信息', () => {
      const calculatePagination = (total: number, limit: number, offset: number) => ({
        total,
        limit,
        offset,
        hasMore: offset + limit < total,
        currentPage: Math.floor(offset / limit) + 1,
        totalPages: Math.ceil(total / limit),
      });

      const pagination = calculatePagination(100, 10, 20);
      
      expect(pagination.currentPage).toBe(3);
      expect(pagination.totalPages).toBe(10);
      expect(pagination.hasMore).toBe(true);
    });
  });

  describe('時間處理', () => {
    it('應該正確格式化時間', () => {
      const formatTime = (timestamp: string) => {
        return new Date(timestamp).toLocaleDateString('zh-TW');
      };

      const formatted = formatTime('2024-06-24T10:30:00Z');
      expect(formatted).toMatch(/2024/);
    });

    it('應該正確計算時間範圍', () => {
      const getTimeRange = (range: string) => {
        const now = new Date();
        const days = {
          '7d': 7,
          '30d': 30,
          '90d': 90,
          '1y': 365,
        }[range] || 30;
        
        return new Date(now.getTime() - days * 24 * 60 * 60 * 1000);
      };

      const sevenDaysAgo = getTimeRange('7d');
      const now = new Date();
      const diffDays = Math.floor((now.getTime() - sevenDaysAgo.getTime()) / (24 * 60 * 60 * 1000));
      
      expect(diffDays).toBe(7);
    });
  });
});

// 導出測試工具函數供其他測試文件使用
export const testUtils = {
  mockDashboardData,
  mockResponseData,
  formatNumber: (num: number) => new Intl.NumberFormat('zh-TW').format(num),
  formatPercentage: (num: number) => `${num.toFixed(1)}%`,
};
